const express = require("express");
const path = require("path");
const http = require("http");
const bodyParser = require("body-parser");
const movieModel = require("./movie-model.js");
const Test = require("./constants.js");

const app = express();

// Parse urlencoded bodies
app.use(bodyParser.json());

// Serve static content in directory 'files'
app.use(express.static(path.join(__dirname, "files")));

app.get("/movies", function (req, res) {
  let movies = Object.values(movieModel);
  const queriedGenre = req.query.genre;
  if (queriedGenre) {
    movies = movies.filter((movie) => movie.Genres.indexOf(queriedGenre) >= 0);
  }
  res.send(movies);
});

// Configure a 'get' endpoint for a specific movie
app.get("/movies/:imdbID", function (req, res) {
  const id = req.params.imdbID;
  const exists = id in movieModel;

  if (exists) {
    res.send(movieModel[id]);
  } else {
    res.sendStatus(404);
  }
});

app.put("/movies/:imdbID", function (req, res) {
  const id = req.params.imdbID;
  const exists = id in movieModel;

  movieModel[req.params.imdbID] = req.body;

  if (!exists) {
    res.status(201);
    res.send(req.body);
  } else {
    res.sendStatus(200);
  }
});

app.get("/genres", function (req, res) {
  const genres = [
    ...new Set(Object.values(movieModel).flatMap((movie) => movie.Genres)),
  ];
  genres.sort();
  res.send(genres);
});

/* Task 1.1. Add the GET /search endpoint: Query omdbapi.com and return
   a list of the results you obtain. Only include the properties 
   mentioned in the README when sending back the results to the client */

// serach enpoint
app.get("/search", async (req, res) => {
  const { query } = req.query;
  if (!query) {
    return res.sendStatus(400);
  }

  const url = `${Test.URLS.OMDB_API}?apikey=${
    Test.API_KEYS.OMDB_API_KEY
  }&s=${encodeURIComponent(query)}`;

  try {
    const apiRes = await fetch(url);
    if (!apiRes.ok) {
      return res.sendStatus(apiRes.status);
    }

    const data = await apiRes.json();
    // Wenn OMDb keinen Treffer findet, gibt data.Response="False"
    const hits = Array.isArray(data.Search) ? data.Search : [];

    const filtered = hits.map((movie) => {
      const numYear = parseInt(movie.Year, 10);
      return {
        Title: movie.Title,
        imdbID: movie.imdbID,
        Year: isNaN(numYear) ? null : numYear,
      };
    });

    res.status(200).json(filtered);
  } catch (err) {
    console.error("OMDb-Error:", err);
    res.sendStatus(502);
  }
});

/* Task 2.2 Add a POST /movies endpoint that receives an array of imdbIDs that the
   user selected to be added to the movie collection. Search them on omdbapi.com,
   convert the data to the format we use since exercise 1 and add the data to the
   movie collection. */

/* Task 3.2. Add the DELETE /movies/:imdbID endpoint which removes the movie
   with the given imdbID from the collection. */

app.listen(3000);

console.log("Server now listening on http://localhost:3000/");
