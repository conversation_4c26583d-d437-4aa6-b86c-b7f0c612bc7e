@import url(base.css);

h1 {
  font-size: 200%;
  margin: 8px 0px;
}

html,
body {
  margin: 0px;
  height: 100vh;
}

/* The nav's header and button list will be centered */
nav {
  display: flex;
  flex-direction: column;
  align-items: center;
}

ul {
  margin-top: 0px;
  padding: 16px;
  list-style-type: none;
}

body {
  display: grid;
  grid-template-rows: 92px auto 64px;
  grid-template-columns: 192px auto;
  grid-template-areas: "h h" "n m" "f f";
  margin: 0;
}

header {
  grid-area: h;
}

nav {
  grid-area: n;
}

main {
  grid-area: m;
  overflow-y: auto;
}

footer {
  grid-area: f;
}

nav > ul {
  display: flex;
  flex-direction: column;
  row-gap: 4px;
}

footer > ul {
  display: flex;
  flex-direction: row;
  column-gap: 16px;
  justify-content: center;
}

header {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* navigation buttons have full width */
nav button {
  width: 100%;
}

img {
  border-radius: 12px;
}

a {
  color: beige;
}
